# PFE瞳孔前缩误差矫正系统

基于论文：Hayes & Petrov (2016) "Mapping and correcting the influence of gaze position on pupil size measurements" Behavior Research Methods

## 概述

PFE（Pupil Foreshortening Error）瞳孔前缩误差矫正系统用于矫正由于眼球转动导致的瞳孔测量误差。当眼睛注视屏幕不同位置时，由于几何前缩效应，瞳孔的视觉面积会发生变化，导致瞳孔测量的系统性误差。

## 核心原理

### 几何模型
根据论文公式，矫正后的瞳孔大小为：
```
corrected_pupil = measured_pupil / √cos(θ)
```

其中θ是眼-相机轴和眼-目标轴之间的夹角：
```
cos(θ) = (C⃗ · T⃗) / (|C⃗| × |T⃗|)
```

- C⃗：眼-相机向量
- T⃗：眼-目标向量

### 插值矫正
基于网格标定数据，使用插值方法建立位置-瞳孔大小的映射关系，对任意注视位置进行矫正。

## 文件结构

```
pfe/
├── config.py                  # 配置参数
├── pfe_calibration.py         # 网格标定程序
├── pfe_data_analyzer.py       # 标定数据分析
├── pfe_correction.py          # PFE矫正模块
├── test_pfe_calibration.py    # 标定程序测试
├── test_pfe_integration.py    # 集成测试
├── calibration_data/          # 标定数据存储目录
│   ├── *.edf                  # EDF眼动数据文件
│   ├── *_calibration_*.csv    # 标定配置文件
│   ├── *_analyzed.csv         # 分析结果文件
│   └── *_report.txt           # 分析报告文件
└── README.md                  # 使用说明
```

## 数据存储

**重要变更**：从2025-07-31开始，所有PFE标定数据统一存储在 `pfe/calibration_data/` 目录中：

- ✅ **标定数据**：EDF文件和CSV配置文件
- ✅ **分析结果**：自动生成的分析文件和报告
- ✅ **缓存文件**：数据预处理缓存（data_cache子目录）
- ✅ **简化管理**：所有相关文件集中存储

## 使用流程

### 1. PFE标定

首先进行网格标定，收集不同注视位置的瞳孔数据：

```bash
# 运行PFE标定程序（数据自动保存到pfe/calibration_data/）
python pfe/pfe_calibration.py --participant 被试ID

# 虚拟模式测试
python pfe/pfe_calibration.py --participant test --dummy
```

标定参数（在`config.py`中配置）：
- 网格大小：8×6 = 48个点
- 每点注视时间：4秒
- 分析窗口：后3秒
- 圆点大小：20像素

### 2. 数据分析

分析EDF文件，提取每个标定点的瞳孔数据：

```bash
# 新的简化参数方式（推荐）
python pfe/pfe_data_analyzer.py --dir pfe/calibration_data

# 旧的方式（仍然支持）
python pfe/pfe_data_analyzer.py --edf 标定.edf --calibration 标定.csv
```

**注意**：
- 标定数据现在统一保存在 `pfe/calibration_data/` 目录
- 分析器会自动查找目录中的EDF和CSV文件
- 只需提供标定数据目录即可

### 3. 应用PFE矫正

#### 方法1：在预处理流程中启用

```python
from analysis.preprocess_edf import EyeDataPreprocessor

# 创建预处理器，启用PFE矫正
preprocessor = EyeDataPreprocessor(
    enable_pfe_correction=True,
    pfe_calibration_data_path='path/to/calibration_analyzed.csv',
    pfe_correction_method='auto'  # 'geometric', 'interpolation', 'hybrid', 'auto'
)

# 预处理数据
processed_data = preprocessor.preprocess_edf('data.edf')
```

#### 方法2：使用便捷函数

```python
from analysis.preprocess_edf import preprocess_edf_file

processed_data = preprocess_edf_file(
    'data.edf',
    enable_pfe_correction=True,
    pfe_calibration_data_path='calibration_analyzed.csv',
    pfe_correction_method='interpolation'
)
```

#### 方法3：独立使用PFE矫正

```python
from pfe.pfe_correction import apply_pfe_correction

corrected_data = apply_pfe_correction(
    samples=eye_data,
    calibration_data_path='calibration_analyzed.csv',
    method='auto'
)
```

## 矫正方法

### 1. 几何模型矫正 (`geometric`)
- 基于相机和屏幕的几何位置
- 使用论文中的数学公式
- 不需要标定数据
- 适用于已知几何参数的情况

### 2. 插值矫正 (`interpolation`)
- 基于网格标定数据
- 使用插值方法建立映射
- 需要完整的标定数据
- 精度更高，适用于实际应用

### 3. 混合矫正 (`hybrid`)
- 结合几何模型和插值方法
- 先应用几何矫正，再用插值微调
- 理论上效果最好

### 4. 自动选择 (`auto`)
- 根据可用数据自动选择方法
- 有标定数据时使用插值，否则使用几何模型

## 配置参数

主要配置在`pfe/config.py`中：

```python
# 网格标定参数
GRID_ROWS = 6
GRID_COLS = 8
FIXATION_DURATION = 4.0
ANALYSIS_WINDOW = 3.0

# 几何模型参数
CAMERA_POSITION = {'x': 40, 'y': -85, 'z': 560}
SCREEN_POSITION = {'x': -200, 'y': 155, 'z': 710}

# 矫正参数
MIN_CORRECTION_FACTOR = 0.5
MAX_CORRECTION_FACTOR = 2.0
INTERPOLATION_METHOD = 'cubic'
```

## 数据格式

### 输入数据要求
- `px_left`, `py_left`：左眼注视位置
- `pa_left`：左眼瞳孔面积（area模式）
- `pa_right`：右眼瞳孔面积（可选）

### 输出数据
矫正后会添加以下列：
- `pa_left_corrected`：矫正后的左眼瞳孔面积
- `pa_right_corrected`：矫正后的右眼瞳孔面积（如果有）

## 质量控制

### 标定质量检查
- 最少有效标定点：36个（75%）
- 数据质量评分：基于有效样本比例
- 注视精度检查：与目标点的距离

### 矫正系数限制
- 最小矫正系数：0.5
- 最大矫正系数：2.0
- 异常值检测和过滤

## 测试和验证

### 运行测试
```bash
# 测试标定程序
python pfe/test_pfe_calibration.py

# 测试集成
python pfe/test_pfe_integration.py

# 测试矫正模块
python pfe/pfe_correction.py
```

### 验证结果
- 检查矫正系数的合理性
- 比较矫正前后的瞳孔数据分布
- 验证不同注视位置的矫正效果

## 注意事项

1. **几何参数设置**：确保相机和屏幕位置参数准确
2. **标定质量**：保证标定过程中头部稳定，注视准确
3. **数据完整性**：确保注视位置数据的完整性
4. **矫正范围**：矫正系数会被限制在合理范围内
5. **性能考虑**：插值矫正比几何矫正计算量更大

## 参考文献

Hayes, T. R., & Petrov, A. A. (2016). Mapping and correcting the influence of gaze position on pupil size measurements. *Behavior Research Methods*, 48(2), 510-527.

## 更新历史

- 2025-07-31：完成PFE矫正系统开发
  - 实现网格标定程序
  - 实现几何和插值矫正方法
  - 集成到预处理流程
  - 完成测试和验证
