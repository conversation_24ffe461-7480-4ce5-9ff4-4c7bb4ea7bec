#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
PFE (Pupil Foreshortening Error) 校正算法
基于论文: Hayes & Petrov (2016) Behav Res

实现几何模型的PFE校正算法，根据注视位置和相机位置计算校正系数
"""

import os
import sys
import numpy as np
import pandas as pd
import logging
from typing import Dict, List, Tuple, Optional, Union
import warnings

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pfe.config import CAMERA_POSITION, SCREEN_POSITION

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('PFE校正')

class PFECorrector:
    """PFE瞳孔前缩误差校正器"""
    
    def __init__(self, 
                 camera_position: Dict[str, float] = None,
                 screen_position: Dict[str, float] = None,
                 screen_width_mm: float = 531.36,
                 screen_height_mm: float = 298.89,
                 screen_width_pixels: int = 1920,
                 screen_height_pixels: int = 1080):
        """
        初始化PFE校正器
        
        Args:
            camera_position: 相机位置 {'x': x, 'y': y, 'z': z} (mm)
            screen_position: 屏幕左上角位置 {'x': x, 'y': y, 'z': z} (mm)
            screen_width_mm: 屏幕物理宽度 (mm)
            screen_height_mm: 屏幕物理高度 (mm)
            screen_width_pixels: 屏幕像素宽度
            screen_height_pixels: 屏幕像素高度
        """
        # 使用配置文件中的默认值或用户提供的值
        self.camera_pos = camera_position or CAMERA_POSITION
        self.screen_pos = screen_position or SCREEN_POSITION
        
        # 屏幕参数
        self.screen_width_pixels = screen_width_pixels
        self.screen_height_pixels = screen_height_pixels
        
        # 如果没有提供物理尺寸，使用常见的估算值
        if screen_width_mm is None or screen_height_mm is None:
            # 假设24寸显示器，16:9比例
            diagonal_inch = 24
            diagonal_mm = diagonal_inch * 25.4
            aspect_ratio = 16/9
            self.screen_width_mm = diagonal_mm * aspect_ratio / np.sqrt(aspect_ratio**2 + 1)
            self.screen_height_mm = self.screen_width_mm / aspect_ratio
            logger.warning(f"未提供屏幕物理尺寸，使用估算值: {self.screen_width_mm:.1f}x{self.screen_height_mm:.1f}mm")
        else:
            self.screen_width_mm = screen_width_mm
            self.screen_height_mm = screen_height_mm
        
        # 计算像素到毫米的转换比例
        self.pixel_to_mm_x = self.screen_width_mm / screen_width_pixels
        self.pixel_to_mm_y = self.screen_height_mm / screen_height_pixels
        
        logger.info(f"PFE校正器初始化完成")
        logger.info(f"相机位置: {self.camera_pos}")
        logger.info(f"屏幕位置: {self.screen_pos}")
        logger.info(f"像素转换比例: {self.pixel_to_mm_x:.3f}mm/pixel (x), {self.pixel_to_mm_y:.3f}mm/pixel (y)")
    
    def pixel_to_mm(self, x_pixel: float, y_pixel: float) -> Tuple[float, float]:
        """
        将像素坐标转换为毫米坐标
        
        Args:
            x_pixel: x像素坐标
            y_pixel: y像素坐标
            
        Returns:
            Tuple[float, float]: (x_mm, y_mm) 毫米坐标
        """
        x_mm = x_pixel * self.pixel_to_mm_x
        y_mm = y_pixel * self.pixel_to_mm_y
        return x_mm, y_mm
    
    # def calculate_gaze_vector(self, x_mm: float, y_mm: float) -> np.ndarray:
    #     """
    #     计算注视向量T (从瞳孔中心到注视点)
        
    #     Args:
    #         x_mm: 注视点x坐标 (mm)
    #         y_mm: 注视点y坐标 (mm)
            
    #     Returns:
    #         np.ndarray: 注视向量 [Tx, Ty, Tz]
    #     """
    #     # 根据论文公式，注视点在眼坐标系中的位置
    #     Tx = x_mm + self.screen_pos['x']
    #     Ty = (-y_mm) + self.screen_pos['y']
    #     Tz = self.screen_pos['z']
        
    #     return np.array([Tx, Ty, Tz])
    
    # def calculate_camera_vector(self) -> np.ndarray:
    #     """
    #     计算相机向量C (从瞳孔中心到相机)
        
    #     Returns:
    #         np.ndarray: 相机向量 [Cx, Cy, Cz]
    #     """
    #     return np.array([self.camera_pos['x'], self.camera_pos['y'], self.camera_pos['z']])
    
    # def calculate_angle_cosine(self, gaze_vector: np.ndarray, camera_vector: np.ndarray) -> float:
    #     """
    #     计算相机轴与注视轴夹角的余弦值
        
    #     Args:
    #         gaze_vector: 注视向量
    #         camera_vector: 相机向量
            
    #     Returns:
    #         float: cos(θ)
    #     """
    #     # 计算向量的模长
    #     gaze_magnitude = np.linalg.norm(gaze_vector)
    #     camera_magnitude = np.linalg.norm(camera_vector)
        
    #     # 避免除零错误
    #     if gaze_magnitude == 0 or camera_magnitude == 0:
    #         return 1.0
        
    #     # 计算点积
    #     dot_product = np.dot(camera_vector, gaze_vector)
        
    #     # 计算余弦值
    #     cos_theta = dot_product / (camera_magnitude * gaze_magnitude)
        
    #     # 确保余弦值在有效范围内
    #     cos_theta = np.clip(cos_theta, -1.0, 1.0)
        
    #     return cos_theta
    
    # def calculate_correction_factor(self, cos_theta: float, use_corneal_correction: bool = False) -> float:
    #     """
    #     计算PFE校正系数

    #     Args:
    #         cos_theta: 夹角余弦值
    #         use_corneal_correction: 是否使用角膜像差修正 (论文公式5)

    #     Returns:
    #         float: 校正系数
    #     """
    #     # 避免负值或零值
    #     if cos_theta <= 0:
    #         logger.warning(f"无效的cos_theta值: {cos_theta}, 使用默认校正系数1.0")
    #         return 1.0

    #     if use_corneal_correction:
    #         # 使用Mathur等人的经验公式 (论文公式5)
    #         # 注意：这里仍然需要开平方根，因为经验公式是基于直径的
    #         theta_degrees = np.degrees(np.arccos(cos_theta))
    #         corrected_cos = 0.992 * np.cos(np.radians((theta_degrees + 5.3) / 1.121))
    #         if corrected_cos <= 0:
    #             return 1.0
    #         correction_factor = 1.0 / np.sqrt(corrected_cos)
    #     else:
    #         # 基础几何校正 (论文公式2) - 针对面积
    #         # EyeLink输出的pa_left是角面积，使用 A₀ = A(x,y) / cos θ
    #         correction_factor = 1.0 / cos_theta

    #     # 限制校正系数的最大值，避免极端情况
    #     max_correction = 5.0  # 面积校正系数可能更大
    #     correction_factor = min(correction_factor, max_correction)

    #     return correction_factor
    
    # def correct_single_sample(self, x_pixel: float, y_pixel: float, pupil_area: float,
    #                         use_corneal_correction: bool = False) -> float:
    #     """
    #     校正单个样本的瞳孔面积
        
    #     Args:
    #         x_pixel: 注视点x像素坐标
    #         y_pixel: 注视点y像素坐标
    #         pupil_area: 原始瞳孔面积
    #         use_corneal_correction: 是否使用角膜像差修正
            
    #     Returns:
    #         float: 校正后的瞳孔面积
    #     """
    #     # 检查输入有效性
    #     if pd.isna(x_pixel) or pd.isna(y_pixel) or pd.isna(pupil_area):
    #         return pupil_area
        
    #     if pupil_area <= 0:
    #         return pupil_area
        
    #     try:
    #         # 转换坐标
    #         x_mm, y_mm = self.pixel_to_mm(x_pixel, y_pixel)
            
    #         # 计算向量
    #         gaze_vector = self.calculate_gaze_vector(x_mm, y_mm)
    #         camera_vector = self.calculate_camera_vector()
            
    #         # 计算夹角余弦
    #         cos_theta = self.calculate_angle_cosine(gaze_vector, camera_vector)
            
    #         # 计算校正系数
    #         correction_factor = self.calculate_correction_factor(cos_theta, use_corneal_correction)
            
    #         # 应用校正
    #         corrected_area = pupil_area * correction_factor
            
    #         return corrected_area
            
    #     except Exception as e:
    #         logger.error(f"单样本校正失败: {e}")
    #         return pupil_area

    def correct_pupil_data(self, samples: pd.DataFrame,
                          use_corneal_correction: bool = False,
                          gaze_x_col: str = 'px_left',
                          gaze_y_col: str = 'py_left',
                          pupil_left_col: str = 'pa_left',
                          pupil_right_col: str = 'pa_right') -> pd.DataFrame:
        """
        批量校正瞳孔数据 (向量化实现)

        Args:
            samples: 包含注视和瞳孔数据的DataFrame
            use_corneal_correction: 是否使用角膜像差修正
            gaze_x_col: 注视x坐标列名
            gaze_y_col: 注视y坐标列名
            pupil_left_col: 左眼瞳孔面积列名
            pupil_right_col: 右眼瞳孔面积列名

        Returns:
            pd.DataFrame: 校正后的数据
        """
        corrected_samples = samples.copy()

        # 检查必要的列是否存在
        if gaze_x_col not in samples.columns or gaze_y_col not in samples.columns:
            logger.warning(f"缺少注视坐标列 {gaze_x_col} 或 {gaze_y_col}，跳过PFE校正")
            return corrected_samples

        # 获取有效数据的掩码
        valid_mask = (
            pd.notna(samples[gaze_x_col]) &
            pd.notna(samples[gaze_y_col])
        )

        if not valid_mask.any():
            logger.warning("没有有效的注视数据，跳过PFE校正")
            return corrected_samples

        try:
            # 向量化计算校正系数
            correction_factors = self._calculate_correction_factors_vectorized(
                samples[gaze_x_col].values,
                samples[gaze_y_col].values,
                valid_mask.values,
                use_corneal_correction
            )

            # 应用校正到左眼数据
            if pupil_left_col in samples.columns:
                left_valid = valid_mask & pd.notna(samples[pupil_left_col]) & (samples[pupil_left_col] > 0)
                corrected_samples.loc[left_valid, pupil_left_col] = (
                    samples.loc[left_valid, pupil_left_col] * correction_factors[left_valid]
                )
                logger.info(f"左眼数据校正完成，校正了 {left_valid.sum()} 个样本")

            # 应用校正到右眼数据
            # if pupil_right_col in samples.columns:
            #     right_valid = valid_mask & pd.notna(samples[pupil_right_col]) & (samples[pupil_right_col] > 0)
            #     corrected_samples.loc[right_valid, pupil_right_col] = (
            #         samples.loc[right_valid, pupil_right_col] * correction_factors[right_valid]
            #     )
            #     logger.info(f"右眼数据校正完成，校正了 {right_valid.sum()} 个样本")

            # 记录校正统计信息
            valid_corrections = correction_factors[valid_mask]
            logger.info(f"PFE校正统计: 平均校正系数={valid_corrections.mean():.3f}, "
                       f"范围=[{valid_corrections.min():.3f}, {valid_corrections.max():.3f}]")

            return corrected_samples

        except Exception as e:
            logger.error(f"批量PFE校正失败: {e}")
            return corrected_samples

    def _calculate_correction_factors_vectorized(self, x_pixels: np.ndarray, y_pixels: np.ndarray,
                                               valid_mask: np.ndarray,
                                               use_corneal_correction: bool = False) -> np.ndarray:
        """
        向量化计算校正系数

        Args:
            x_pixels: x像素坐标数组
            y_pixels: y像素坐标数组
            valid_mask: 有效数据掩码
            use_corneal_correction: 是否使用角膜像差修正

        Returns:
            np.ndarray: 校正系数数组
        """
        n_samples = len(x_pixels)
        correction_factors = np.ones(n_samples)

        if not valid_mask.any():
            return correction_factors

        # 只处理有效数据
        valid_x = x_pixels[valid_mask]
        valid_y = y_pixels[valid_mask]

        # 转换为毫米坐标
        x_mm = valid_x * self.pixel_to_mm_x
        y_mm = valid_y * self.pixel_to_mm_y

        # 计算注视向量 (向量化)
        Tx = x_mm + self.screen_pos['x']
        Ty = (-y_mm) + self.screen_pos['y'] #eyelink像素坐标是x向右，y向下；我的屏幕左上角的坐标系是x向右，y向上，z向内
        Tz = np.full_like(Tx, self.screen_pos['z'])

        # 相机向量 (常量)
        Cx, Cy, Cz = self.camera_pos['x'], self.camera_pos['y'], self.camera_pos['z']

        # 计算点积
        dot_products = Cx * Tx + Cy * Ty + Cz * Tz

        # 计算向量模长
        gaze_magnitudes = np.sqrt(Tx**2 + Ty**2 + Tz**2)
        camera_magnitude = np.sqrt(Cx**2 + Cy**2 + Cz**2)

        # 计算余弦值
        cos_theta = dot_products / (camera_magnitude * gaze_magnitudes)
        cos_theta = np.clip(cos_theta, -1.0, 1.0)
        
        theta = (np.arccos(cos_theta)/np.pi*180)
        print("theta",theta)
        print("cos theta", cos_theta)
        print("staring point", Tx, Ty, Tz)
        print("camera vector", Cx, Cy, Cz)
        print("x mm", x_mm)
        print("y mm", y_mm)
        print("valid x", valid_x)
        print("valid y", valid_y)
        print("x x_pixels", x_pixels)
        print("y y_pixels", y_pixels)
        
        # 过滤无效值
        valid_cos = cos_theta > 0

        if use_corneal_correction:
            # 角膜像差修正 - 仍然使用开平方根，因为经验公式基于直径
            theta_degrees = np.degrees(np.arccos(cos_theta[valid_cos]))
            corrected_cos = 0.992 * np.cos(np.radians((theta_degrees + 5.3) / 1.121))
            corrected_cos = np.maximum(corrected_cos, 1e-6)  # 避免负值
            valid_corrections = 1.0 / np.sqrt(corrected_cos)
        else:
            # 基础几何校正 (论文公式2) - 针对面积
            # EyeLink输出的pa_left是角面积，使用 A₀ = A(x,y) / cos θ
            valid_corrections = 1.0 / cos_theta[valid_cos]

        # 限制最大校正系数 - 面积校正系数可能更大
        valid_corrections = np.minimum(valid_corrections, 5.0)

        # 将结果填回原数组
        valid_indices = np.where(valid_mask)[0]
        valid_cos_indices = valid_indices[valid_cos]
        correction_factors[valid_cos_indices] = valid_corrections

        return correction_factors


def apply_pfe_correction(samples: pd.DataFrame,
        enable_correction: bool = True,
                        use_corneal_correction: bool = False,
                        camera_position: Dict[str, float] = None,
                        screen_position: Dict[str, float] = None,
                        screen_width_mm: float = None,
                        screen_height_mm: float = None,
                        **kwargs) -> pd.DataFrame:
    """
    应用PFE校正的主要接口函数

    Args:
        samples: 包含瞳孔和注视数据的DataFrame
        calibration_data_path: 标定数据路径 (暂未使用，为兼容性保留)
        enable_correction: 是否启用校正
        method: 校正方法 ('geometric' 或其他，当前只支持geometric)
        use_corneal_correction: 是否使用角膜像差修正
        camera_position: 相机位置覆盖
        screen_position: 屏幕位置覆盖
        screen_width_mm: 屏幕宽度覆盖
        screen_height_mm: 屏幕高度覆盖
        **kwargs: 其他参数

    Returns:
        pd.DataFrame: 校正后的数据
    """
    if not enable_correction:
        logger.info("PFE校正已禁用")
        return samples

    try:
        # 创建校正器
        corrector = PFECorrector(
            camera_position=camera_position,
            screen_position=screen_position,
            screen_width_mm=screen_width_mm,
            screen_height_mm=screen_height_mm,
            screen_width_pixels=kwargs.get('screen_width_pixels', 1920),
            screen_height_pixels=kwargs.get('screen_height_pixels', 1080)
        )

        # 应用校正
        corrected_samples = corrector.correct_pupil_data(
            samples,
            use_corneal_correction=use_corneal_correction
        )

        logger.info("PFE校正应用完成")
        return corrected_samples

    except Exception as e:
        logger.error(f"PFE校正应用失败: {e}")
        return samples


def calculate_pfe_statistics(samples: pd.DataFrame,
                           corrected_samples: pd.DataFrame,
                           pupil_col: str = 'pa_left') -> Dict[str, float]:
    """
    计算PFE校正的统计信息

    Args:
        samples: 原始数据
        corrected_samples: 校正后数据
        pupil_col: 瞳孔列名

    Returns:
        Dict[str, float]: 统计信息
    """
    if pupil_col not in samples.columns or pupil_col not in corrected_samples.columns:
        return {}

    original = samples[pupil_col].dropna()
    corrected = corrected_samples[pupil_col].dropna()

    if len(original) == 0 or len(corrected) == 0:
        return {}

    # 计算校正系数
    correction_factors = corrected / original
    correction_factors = correction_factors[np.isfinite(correction_factors)]

    stats = {
        'mean_correction_factor': correction_factors.mean(),
        'std_correction_factor': correction_factors.std(),
        'min_correction_factor': correction_factors.min(),
        'max_correction_factor': correction_factors.max(),
        'original_mean': original.mean(),
        'corrected_mean': corrected.mean(),
        'relative_change_percent': ((corrected.mean() - original.mean()) / original.mean()) * 100
    }

    return stats


def validate_pfe_inputs(samples: pd.DataFrame) -> bool:
    """
    验证PFE校正的输入数据

    Args:
        samples: 输入数据

    Returns:
        bool: 是否有效
    """
    required_cols = ['px_left', 'py_left']
    pupil_cols = ['pa_left', 'pa_right']

    # 检查必要列
    missing_cols = [col for col in required_cols if col not in samples.columns]
    if missing_cols:
        logger.error(f"缺少必要的列: {missing_cols}")
        return False

    # 检查是否有瞳孔数据
    has_pupil_data = any(col in samples.columns for col in pupil_cols)
    if not has_pupil_data:
        logger.error(f"缺少瞳孔数据列: {pupil_cols}")
        return False

    # 检查数据量
    if len(samples) == 0:
        logger.error("输入数据为空")
        return False

    return True


if __name__ == "__main__":
    # 测试代码
    print("PFE校正模块测试")

    # 创建测试数据
    test_data = pd.DataFrame([
        {
            'px_left': 400,
            'py_left': 130,
            'pa_left': 1,
            'pa_right': 1,
        }
    ])

    print(f"测试数据: {len(test_data)} 个样本")

    # 应用校正
    corrected_data = apply_pfe_correction(test_data, enable_correction=True)

    # 计算统计信息
    stats = calculate_pfe_statistics(test_data, corrected_data, 'pa_left')
    print("校正统计信息:")
    for key, value in stats.items():
        print(f"  {key}: {value:.3f}")

    print("测试完成")
